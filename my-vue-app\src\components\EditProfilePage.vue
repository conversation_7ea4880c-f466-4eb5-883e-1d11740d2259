<template>
  <div class="edit-profile-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-container">
          <img :src="signalIcon" alt="信号" class="signal-icon" />
          <img :src="signalBar1" alt="信号条1" class="signal-bar" />
          <img :src="signalBar2" alt="信号条2" class="signal-bar" />
          <img :src="signalBar3" alt="信号条3" class="signal-bar" />
        </div>
      </div>
      <div class="status-right">
        <div class="battery-container">
          <img :src="batteryOutline" alt="电池外框" class="battery-outline" />
          <img :src="batteryFill1" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill2" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill3" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill4" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill5" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill6" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill7" alt="电池电量" class="battery-fill" />
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <img :src="arrowLeft1" alt="返回" class="arrow-left-1" />
      <img :src="arrowLeft2" alt="返回" class="arrow-left-2" />
    </div>



    <!-- 页面标题 -->
    <div class="page-title">编辑个人信息</div>

    <!-- 用户头像 -->
    <div class="user-avatar">
      <img :src="avatar" alt="用户头像" />
    </div>

    <!-- 表单项 -->
    <div class="form-section">
      <!-- 成员关系 -->
      <div class="form-item">
        <div class="form-label">成员关系</div>
        <div class="form-value">
          <div class="tag">妈妈</div>
        </div>
        <img :src="caretRight" alt="右箭头" class="caret-right" />
        <div class="form-divider"></div>
      </div>

      <!-- 名字 -->
      <div class="form-item">
        <div class="form-label">名字</div>
        <div class="form-value">Rachel</div>
        <img :src="caretRight" alt="右箭头" class="caret-right" />
        <div class="form-divider"></div>
      </div>

      <!-- 性别 -->
      <div class="form-item">
        <div class="form-label">性别</div>
        <div class="form-value">
          <img :src="wechatLogo" alt="微信" class="wechat-icon" />
          <span>女</span>
        </div>
        <img :src="caretRight" alt="右箭头" class="caret-right" />
        <div class="form-divider"></div>
      </div>

      <!-- 出生年月 -->
      <div class="form-item">
        <div class="form-label">出生年月</div>
        <div class="form-value">1900.01.01</div>
        <img :src="caretRight" alt="右箭头" class="caret-right" />
        <div class="form-divider"></div>
      </div>

      <!-- 手机号码 -->
      <div class="form-item">
        <div class="form-label">手机号码</div>
        <div class="form-value">1386****000</div>
        <img :src="caretRight" alt="右箭头" class="caret-right" />
        <div class="form-divider"></div>
      </div>
    </div>

    <!-- 保存按钮 -->
    <div class="save-button" @click="saveProfile">
      <span>保存</span>
    </div>
  </div>
</template>

<script>
// 导入图片资源
import signalIcon from '../assets/images/signal-icon.svg'
import signalBar1 from '../assets/images/signal-bar1.svg'
import signalBar2 from '../assets/images/signal-bar2.svg'
import signalBar3 from '../assets/images/signal-bar3.svg'
import batteryOutline from '../assets/images/battery-outline.svg'
import batteryFill1 from '../assets/images/battery-fill1.svg'
import batteryFill2 from '../assets/images/battery-fill2.svg'
import batteryFill3 from '../assets/images/battery-fill3.svg'
import batteryFill4 from '../assets/images/battery-fill4.svg'
import batteryFill5 from '../assets/images/battery-fill5.svg'
import batteryFill6 from '../assets/images/battery-fill6.svg'
import batteryFill7 from '../assets/images/battery-fill7.svg'
import arrowLeft1 from '../assets/images/arrow-left-line1.svg'
import arrowLeft2 from '../assets/images/arrow-left-line2.svg'
import avatar from '../assets/images/avatar-56586a.png'
import caretRight from '../assets/images/caret-right.svg'
import wechatLogo from '../assets/images/wechat-logo.svg'

export default {
  name: 'EditProfilePage',
  emits: ['go-back', 'save-profile'],
  data() {
    return {
      // 状态栏图标
      signalIcon,
      signalBar1,
      signalBar2,
      signalBar3,
      batteryOutline,
      batteryFill1,
      batteryFill2,
      batteryFill3,
      batteryFill4,
      batteryFill5,
      batteryFill6,
      batteryFill7,
      // 其他图标
      arrowLeft1,
      arrowLeft2,
      avatar,
      caretRight,
      wechatLogo
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    },
    saveProfile() {
      this.$emit('save-profile')
    }
  }
}
</script>

<style scoped>
.edit-profile-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
  border-bottom: 1px solid #BCC1CA;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-container {
  position: relative;
  width: 27.34px;
  height: 10.7px;
}

.signal-icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 7.86px;
  height: 10.7px;
}

.signal-bar {
  position: absolute;
}

.signal-bar:nth-child(2) {
  left: 9.59px;
  top: 1.61px;
  width: 2.25px;
  height: 7.47px;
}

.signal-bar:nth-child(3) {
  left: 13.53px;
  top: 0.25px;
  width: 8.1px;
  height: 10.19px;
}

.signal-bar:nth-child(4) {
  left: 22.86px;
  top: 0.25px;
  width: 4.48px;
  height: 10.19px;
}

.status-right {
  display: flex;
  align-items: center;
}

.battery-container {
  position: relative;
  width: 65.87px;
  height: 10.56px;
}

.battery-outline {
  position: absolute;
  left: 43.05px;
  top: 0;
  width: 20.28px;
  height: 10.06px;
  opacity: 0.35;
}

.battery-fill {
  position: absolute;
}

.battery-fill:nth-child(2) {
  left: 64.68px;
  top: 3.75px;
  width: 1.19px;
  height: 3.59px;
  opacity: 0.4;
}

.battery-fill:nth-child(3) {
  left: 44.26px;
  top: 1.2px;
  width: 17.87px;
  height: 7.66px;
}

.battery-fill:nth-child(4) {
  left: 22.13px;
  top: 0.35px;
  width: 14.47px;
  height: 10.07px;
}

.battery-fill:nth-child(5) {
  left: 8.51px;
  top: 2.05px;
  width: 2.55px;
  height: 8.51px;
}

.battery-fill:nth-child(6) {
  left: 12.77px;
  top: 0.35px;
  width: 2.55px;
  height: 10.21px;
}

.battery-fill:nth-child(7) {
  left: 4.26px;
  top: 5.03px;
  width: 2.55px;
  height: 5.53px;
}

.battery-fill:nth-child(8) {
  left: 0;
  top: 7.16px;
  width: 2.55px;
  height: 3.4px;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  left: 24px;
  top: 48px;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.arrow-left-1 {
  position: absolute;
  left: 3.4px;
  top: 11.98px;
  width: 17.2px;
  height: 0;
}

.arrow-left-2 {
  position: absolute;
  left: 3.42px;
  top: 5.98px;
  width: 6.02px;
  height: 12.04px;
}

/* 分隔线 */
.separator {
  position: absolute;
  left: 0;
  top: 80px;
  width: 375px;
  height: 4px;
  background: #F8F9FA;
}

/* 页面标题 */
.page-title {
  position: absolute;
  left: 140px;
  top: 46px;
  width: 96px;
  height: 26px;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #323842;
}

/* 用户头像 */
.user-avatar {
  position: absolute;
  left: 158px;
  top: 101px;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  border: 1px solid #636AE8;
  background: #CED0F8;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 表单区域 */
.form-section {
  position: absolute;
  left: 24px;
  top: 189px;
  width: 327px;
}

.form-item {
  position: relative;
  width: 327px;
  height: 36px;
  background: #FFFFFF;
  border-radius: 6px 6px 0px 0px;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.form-label {
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
  width: 56px;
}

.form-value {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
}

.tag {
  background: #636AE8;
  border-radius: 14px;
  padding: 4px 18px;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #FFFFFF;
}

.wechat-icon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}

.caret-right {
  width: 16px;
  height: 16px;
  margin-left: 12px;
}

.form-divider {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 327px;
  height: 1px;
  background: #F3F4F6;
}

/* 保存按钮 */
.save-button {
  position: absolute;
  left: 13px;
  top: 715px;
  width: 350px;
  height: 52px;
  background: #636AE8;
  border-radius: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0px 4px 9px 0px rgba(99, 106, 232, 0.11), 0px 0px 2px 0px rgba(99, 106, 232, 0.12);
}

.save-button span {
  font-family: Inter;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
}
</style>
